import { LoginFormData, User } from '@/types';

// Simple email validation regex
const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

/**
 * Validates if the provided email is in a valid format
 */
export const isValidEmail = (email: string): boolean => {
  return EMAIL_REGEX.test(email);
};

/**
 * Mock authentication function - accepts any valid email and non-empty password
 */
export const authenticateUser = async (credentials: LoginFormData): Promise<User> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000));

  if (!isValidEmail(credentials.email)) {
    throw new Error('Please enter a valid email address');
  }

  if (!credentials.password || credentials.password.trim().length === 0) {
    throw new Error('Password is required');
  }

  // Mock successful authentication - return a user object
  return {
    id: '1',
    email: credentials.email,
    name: credentials.email.split('@')[0], // Use email prefix as name
  };
};

/**
 * Simple session management (in real app, this would use proper tokens/cookies)
 */
export const setUserSession = (user: User): void => {
  localStorage.setItem('user', JSON.stringify(user));
};

export const getUserSession = (): User | null => {
  try {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  } catch {
    return null;
  }
};

export const clearUserSession = (): void => {
  localStorage.removeItem('user');
};

/**
 * Logout function that clears session and returns success
 */
export const logoutUser = (): void => {
  clearUserSession();
};
